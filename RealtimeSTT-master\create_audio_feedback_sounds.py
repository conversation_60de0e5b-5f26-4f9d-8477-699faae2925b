#!/usr/bin/env python3
"""
Audio Feedback Sound Generator for Wake Word Auto-Typing

This script generates simple audio feedback sounds for the wake word detection system.
It creates three different tones:
1. wake_detected.wav - Rising tone when "jarvis" is detected
2. listening_start.wav - Short beep when listening starts
3. listening_stop.wav - Descending tone when listening stops

Run this script once to generate the audio files in the sounds/ directory.
"""

import numpy as np
import wave
import os

def generate_tone(frequency, duration, sample_rate=22050, amplitude=0.3):
    """Generate a simple sine wave tone"""
    frames = int(duration * sample_rate)
    arr = np.zeros(frames)
    
    for i in range(frames):
        arr[i] = amplitude * np.sin(2 * np.pi * frequency * i / sample_rate)
    
    return arr

def generate_rising_tone(start_freq, end_freq, duration, sample_rate=22050, amplitude=0.3):
    """Generate a rising tone that sweeps from start_freq to end_freq"""
    frames = int(duration * sample_rate)
    arr = np.zeros(frames)
    
    for i in range(frames):
        # Linear frequency sweep
        progress = i / frames
        current_freq = start_freq + (end_freq - start_freq) * progress
        arr[i] = amplitude * np.sin(2 * np.pi * current_freq * i / sample_rate)
    
    return arr

def generate_descending_tone(start_freq, end_freq, duration, sample_rate=22050, amplitude=0.3):
    """Generate a descending tone that sweeps from start_freq to end_freq"""
    frames = int(duration * sample_rate)
    arr = np.zeros(frames)
    
    for i in range(frames):
        # Linear frequency sweep
        progress = i / frames
        current_freq = start_freq + (end_freq - start_freq) * progress
        arr[i] = amplitude * np.sin(2 * np.pi * current_freq * i / sample_rate)
    
    return arr

def save_wav_file(filename, audio_data, sample_rate=22050):
    """Save audio data as a WAV file"""
    # Convert to 16-bit integers
    audio_data = (audio_data * 32767).astype(np.int16)
    
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 2 bytes per sample (16-bit)
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_data.tobytes())

def create_audio_feedback_sounds():
    """Create all audio feedback sound files"""
    sounds_dir = "sounds"
    
    # Create sounds directory if it doesn't exist
    if not os.path.exists(sounds_dir):
        os.makedirs(sounds_dir)
    
    print("Generating audio feedback sounds...")
    
    # 1. Wake word detected - Rising tone (pleasant, attention-getting)
    print("  Creating wake_detected.wav...")
    wake_detected = generate_rising_tone(400, 800, 0.3)  # 400Hz to 800Hz over 0.3 seconds
    save_wav_file(os.path.join(sounds_dir, "wake_detected.wav"), wake_detected)
    
    # 2. Listening start - Short beep (clear, brief)
    print("  Creating listening_start.wav...")
    listening_start = generate_tone(1000, 0.1)  # 1000Hz for 0.1 seconds
    save_wav_file(os.path.join(sounds_dir, "listening_start.wav"), listening_start)
    
    # 3. Listening stop - Descending tone (indicates end/completion)
    print("  Creating listening_stop.wav...")
    listening_stop = generate_descending_tone(600, 300, 0.2)  # 600Hz to 300Hz over 0.2 seconds
    save_wav_file(os.path.join(sounds_dir, "listening_stop.wav"), listening_stop)
    
    print("Audio feedback sounds created successfully!")
    print(f"Files saved in: {os.path.abspath(sounds_dir)}")
    print("\nSound descriptions:")
    print("  - wake_detected.wav: Rising tone when 'jarvis' is detected")
    print("  - listening_start.wav: Short beep when recording starts")
    print("  - listening_stop.wav: Descending tone when recording stops")

if __name__ == "__main__":
    try:
        create_audio_feedback_sounds()
    except ImportError as e:
        print("Error: numpy is required to generate audio files.")
        print("Install it with: pip install numpy")
        print(f"Details: {e}")
    except Exception as e:
        print(f"Error creating audio files: {e}")
